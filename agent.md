## Agent Guide

This file orients AI/code agents and humans to contribute effectively to this repo.

### Project Purpose

- **Goal**: Interactive personal finance modeling app. Users input expenses and assumptions to visualize costs, retirement projections, and sustainability.
- **Core features**: Cost calculation flows, retirement projections, scenario comparison, sustainability analysis, and charts.

### Tech Stack

- **Build**: Vite + TypeScript + React 18
- **UI**: shadcn/ui (Radix primitives), Tailwind CSS
- **State**: Zustand stores
- **Charts**: `recharts`
- **Validation/Forms**: `react-hook-form`, `zod`

### How to Run

- **Dev**: `npm run dev`
- **Build (prod)**: `npm run build`
- **Build (dev mode)**: `npm run build:dev`
- **Preview**: `npm run preview`
- **Lint**: `npm run lint`

### Notable App Entry Points

- `src/main.tsx`: App bootstrap
- `src/App.tsx`: Global layout/composition
- `src/pages/Index.tsx`: Primary landing/feature composition
- `src/pages/NotFound.tsx`: 404 route

### Key Domains and Modules

- **Cost Calculation**

  - Components: `src/components/cost-calculation/`
    - `AddExpenseForm.tsx`, `ExpenseCard.tsx`, `CostResults.tsx`
  - Hook: `src/hooks/useCostCalculation.ts`
  - Store: `src/stores/useCostCalculationStore.ts`
  - Types: `src/types/costCalculation.ts`
  - Constants: `src/constants/defaultExpenses.ts`
  - Helpers/Utils: `src/utils/costDataHelpers.ts`

- **Retirement & Projections**

  - Components: `src/components/ProjectionChart.tsx`, `RetirementCalculator.tsx`, `ScenarioComparison.tsx`, `SustainabilityAnalysis.tsx`
  - Hook: `src/hooks/useRetirementCalculations.ts`
  - Store: `src/stores/useRetirementStore.ts`
  - Utils: `src/utils/retirementCalculations.ts`

- **UI Primitives**
  - `src/components/ui/*` (shadcn-generated components)
  - Shared: `src/lib/utils.ts`, `src/constants/iconMap.ts`

### Architectural Notes

- **Data flow**: Components use domain hooks which coordinate with Zustand stores; calculation-heavy logic lives in `src/utils/*` to stay pure/testable.
- **Separation**: Keep side effects (I/O, toast) in hooks/components; keep math in utils.
- **Types first**: Update `src/types/*` before adding fields to state or forms.

### Coding Standards

- **TypeScript**: Prefer explicit function signatures for exports; avoid `any`.
- **Naming**: Descriptive names; avoid abbreviations; follow domain language.
- **Control flow**: Use guard clauses; handle edge cases early; avoid deep nesting.
- **Comments**: Explain "why" for complex logic; keep code self-explanatory.
- **Formatting**: Respect existing style; do not reformat unrelated files.

### UI/UX Conventions

- Use shadcn UI components from `src/components/ui/*` for consistency.
- Keep forms controlled via `react-hook-form`; validation via `zod` where applicable.
- Recharts for visualization; keep chart data preparation in utils.

### State Management

- Centralize domain state in the relevant Zustand store.
- Derive values with selectors/memoization in hooks; avoid duplicating computed state.

### Testing/Verification

- Run `npm run lint` before commit.
- Run `npm run test` before commit.
- Fix any errors before committing.

### Folder Overview

- `src/components/` domain components and UI primitives
- `src/hooks/` domain hooks encapsulating business logic
- `src/stores/` Zustand stores per domain
- `src/utils/` pure helpers for calculations and transforms
- `src/constants/` defaults and static maps
- `src/pages/` route-level composition

### Environment/Config

- Vite config: `vite.config.ts`
- Tailwind: `tailwind.config.ts`, `postcss.config.js`, `src/index.css`
- ESLint config: `eslint.config.js`

### Build/Deploy

- Local: `npm run dev` and open the printed URL.
- Static build: `npm run build` produces `dist/` for static hosting.
- Lovable: see `README.md` for publish steps via the Lovable UI.

### Contribution Checklist

- Update types, then state, then UI, then calculations.
- Keep utils pure; avoid coupling to React or stores in utils.
- Add small, focused commits with clear messages.
- Ensure `npm run lint` passes and app runs locally.

### Quick Links

- Main page composition: `src/pages/Index.tsx`
- Projection chart: `src/components/ProjectionChart.tsx`
- Cost form: `src/components/cost-calculation/AddExpenseForm.tsx`

If something is unclear, prefer adding small clarifying docstrings near the changed code.
