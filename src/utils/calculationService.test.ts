import {
  CalculationService,
  calculateCosts,
  calculateYearlyBreakdowns,
} from "./calculationService";
import { ExpenseCategory } from "@/types/costCalculation";
import { INFLATION_RATE } from "@/constants/finance";

describe("CalculationService", () => {
  const mockExpenses: ExpenseCategory[] = [
    {
      id: "1",
      name: "Food",
      iconName: "Utensils",
      monthlyAmount: 10000,
      frequency: "monthly",
    },
    {
      id: "2",
      name: "Vacation",
      iconName: "Plane",
      monthlyAmount: 50000,
      frequency: "yearly",
    },
    {
      id: "3",
      name: "Laptop",
      iconName: "Laptop",
      monthlyAmount: 80000,
      frequency: "one-time",
      yearsInterval: 4,
    },
  ];

  const defaultParams = {
    retirementAge: 60,
    retirementDuration: 20,
    currentAge: 30,
    inflationRate: 6,
    annualReturn: 12,
  };

  describe("Constructor", () => {
    it("should initialize with provided parameters", () => {
      const service = new CalculationService(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );

      expect(service).toBeInstanceOf(CalculationService);
    });

    it("should use default inflation rate when not provided", () => {
      const service = new CalculationService(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge
      );

      const result = service.calculateAll();
      expect(result).toBeDefined();
    });
  });

  describe("calculateAll", () => {
    let service: CalculationService;

    beforeEach(() => {
      service = new CalculationService(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );
    });

    it("should return all required result properties", () => {
      const result = service.calculateAll();

      expect(result).toHaveProperty("yearlyBreakdowns");
      expect(result).toHaveProperty("aggregatedResults");
      expect(result).toHaveProperty("costResults");
      expect(result).toHaveProperty("oneTimeOccurrences");
    });

    it("should calculate yearly breakdowns for each retirement year", () => {
      const result = service.calculateAll();

      expect(result.yearlyBreakdowns).toHaveLength(
        defaultParams.retirementDuration
      );

      result.yearlyBreakdowns.forEach((breakdown, index) => {
        expect(breakdown.year).toBe(index + 1);
        expect(breakdown.age).toBe(defaultParams.retirementAge + index);
        expect(breakdown.monthlyExpenses).toBeGreaterThan(0);
        expect(breakdown.totalYearlyExpenses).toBeGreaterThan(0);
      });
    });

    it("should calculate aggregated results correctly", () => {
      const result = service.calculateAll();
      const { aggregatedResults } = result;

      expect(aggregatedResults.totalRetirementCost).toBeGreaterThan(0);
      expect(aggregatedResults.breakdown.monthly).toBeGreaterThan(0);
      expect(aggregatedResults.breakdown.yearly).toBeGreaterThan(0);
      expect(aggregatedResults.breakdown.oneTime).toBeGreaterThan(0);
      expect(aggregatedResults.averageAnnualCost).toBeGreaterThan(0);
      expect(aggregatedResults.currentAnnualCost).toBeGreaterThan(0);
    });

    it("should calculate cost results with proper structure", () => {
      const result = service.calculateAll();
      const { costResults } = result;

      expect(costResults.currentMonthlyCost).toBe(10000); // Monthly food expense
      expect(costResults.currentAnnualCost).toBeGreaterThan(0);
      expect(costResults.adjustedMonthlyCost).toBeGreaterThan(
        costResults.currentMonthlyCost
      );
      expect(costResults.totalRetirementCost).toBeGreaterThan(0);
      expect(costResults.breakdown).toHaveProperty("monthly");
      expect(costResults.breakdown).toHaveProperty("yearly");
      expect(costResults.breakdown).toHaveProperty("oneTime");
      expect(costResults.detailedBreakdown).toBeDefined();
    });

    it("should handle one-time expenses correctly", () => {
      const result = service.calculateAll();

      // Should have one-time occurrences for laptop every 4 years
      expect(result.oneTimeOccurrences.length).toBeGreaterThan(0);

      const laptopOccurrences = result.oneTimeOccurrences.filter(
        (occ) => occ.expenseName === "Laptop"
      );

      // In 20 years with 4-year intervals, should have 5 occurrences (years 4, 8, 12, 16, 20)
      expect(laptopOccurrences).toHaveLength(5);
    });

    it("should apply inflation correctly over time", () => {
      const result = service.calculateAll();
      const { yearlyBreakdowns } = result;

      // First year should have lower costs than last year due to inflation
      const firstYear = yearlyBreakdowns[0];
      const lastYear = yearlyBreakdowns[yearlyBreakdowns.length - 1];

      expect(lastYear.monthlyExpenses).toBeGreaterThan(
        firstYear.monthlyExpenses
      );
      expect(lastYear.yearlyExpenses).toBeGreaterThan(firstYear.yearlyExpenses);
    });

    it("should calculate detailed breakdown correctly", () => {
      const result = service.calculateAll();
      const { detailedBreakdown } = result.costResults;

      expect(detailedBreakdown.currentYearlyCost).toBe(50000); // Vacation expense
      expect(detailedBreakdown.adjustedYearlyCost).toBeGreaterThan(
        detailedBreakdown.currentYearlyCost
      );
      expect(detailedBreakdown.yearlyExpensesByCategory).toHaveLength(1);
      expect(detailedBreakdown.oneTimeExpensesByCategory).toHaveLength(1);

      const yearlyExpense = detailedBreakdown.yearlyExpensesByCategory[0];
      expect(yearlyExpense.name).toBe("Vacation");
      expect(yearlyExpense.frequency).toBe("yearly");

      const oneTimeExpense = detailedBreakdown.oneTimeExpensesByCategory[0];
      expect(oneTimeExpense.name).toBe("Laptop");
      expect(oneTimeExpense.frequency).toBe("one-time");
      expect(oneTimeExpense.yearsInterval).toBe(4);
      expect(oneTimeExpense.totalOccurrences).toBe(5);
    });
  });

  describe("generateEnhancedProjections", () => {
    let service: CalculationService;

    beforeEach(() => {
      service = new CalculationService(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );
    });

    it("should generate enhanced projections with retirement data", () => {
      const mockProjectionData = {
        yearlyProjections: [
          {
            age: 30,
            year: 2024,
            portfolioValue: 500000,
            realValue: 500000,
            cumulativeContributions: 100000,
          },
        ],
        finalValue: 5000000,
        totalContributions: 1000000,
        monthlyRetirementIncome: 50000,
        realMonthlyIncome: 30000,
        currentAge: 30,
      };

      const oneTimeOccurrences = [
        {
          age: 62,
          year: 2026,
          expenseName: "Laptop",
          amount: 100000,
          totalAmount: 100000,
        },
      ];

      const adjustedAnnualCost = 500000;

      const result = service.generateEnhancedProjections(
        mockProjectionData,
        oneTimeOccurrences,
        adjustedAnnualCost
      );

      expect(result).toHaveLength(defaultParams.retirementDuration + 1);

      // Check retirement years have proper structure
      const retirementYears = result.filter((point) => point.isRetirement);
      expect(retirementYears).toHaveLength(defaultParams.retirementDuration);

      retirementYears.forEach((point) => {
        expect(point.annualCost).toBe(adjustedAnnualCost);
        expect(point.consolidatedCosts).toBeGreaterThanOrEqual(
          adjustedAnnualCost
        );
        expect(point.incomeFromInterest).toBeGreaterThan(0);
        expect(point.netPosition).toBeDefined();
      });
    });

    it("should handle one-time costs in projections", () => {
      const mockProjectionData = {
        yearlyProjections: [
          {
            age: 30,
            year: 2024,
            portfolioValue: 500000,
            realValue: 500000,
            cumulativeContributions: 100000,
          },
        ],
        finalValue: 5000000,
        totalContributions: 1000000,
        monthlyRetirementIncome: 50000,
        realMonthlyIncome: 30000,
        currentAge: 30,
      };

      const oneTimeOccurrences = [
        {
          age: 61, // First year of retirement
          year: 2025,
          expenseName: "Laptop",
          amount: 100000,
          totalAmount: 100000,
        },
      ];

      const adjustedAnnualCost = 500000;

      const result = service.generateEnhancedProjections(
        mockProjectionData,
        oneTimeOccurrences,
        adjustedAnnualCost
      );

      const firstRetirementYear = result.find((point) => point.age === 61);
      expect(firstRetirementYear?.oneTimeCosts).toBe(100000);
      expect(firstRetirementYear?.consolidatedCosts).toBe(
        adjustedAnnualCost + 100000
      );
    });
  });

  describe("Edge Cases", () => {
    it("should handle empty expenses array", () => {
      const service = new CalculationService(
        [],
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );

      const result = service.calculateAll();

      expect(result.aggregatedResults.totalRetirementCost).toBe(0);
      expect(result.costResults.currentMonthlyCost).toBe(0);
      expect(result.oneTimeOccurrences).toHaveLength(0);
    });

    it("should handle zero inflation rate", () => {
      const service = new CalculationService(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        0, // Zero inflation
        defaultParams.annualReturn
      );

      const result = service.calculateAll();
      const { yearlyBreakdowns } = result;

      // With zero inflation, all years should have same monthly expenses
      const firstYear = yearlyBreakdowns[0];
      const lastYear = yearlyBreakdowns[yearlyBreakdowns.length - 1];

      expect(firstYear.monthlyExpenses).toBeCloseTo(
        lastYear.monthlyExpenses,
        2
      );
    });

    it("should handle one-time expenses with large intervals", () => {
      const expensesWithLargeInterval: ExpenseCategory[] = [
        {
          id: "1",
          name: "House Renovation",
          iconName: "Home",
          monthlyAmount: 1000000,
          frequency: "one-time",
          yearsInterval: 25, // Longer than retirement duration
        },
      ];

      const service = new CalculationService(
        expensesWithLargeInterval,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );

      const result = service.calculateAll();

      // Should have no one-time occurrences since interval > retirement duration
      expect(result.oneTimeOccurrences).toHaveLength(0);
      expect(result.aggregatedResults.breakdown.oneTime).toBe(0);
    });

    it("should handle one-time expenses without yearsInterval", () => {
      const expensesWithoutInterval: ExpenseCategory[] = [
        {
          id: "1",
          name: "Invalid One-time",
          iconName: "Home",
          monthlyAmount: 1000000,
          frequency: "one-time",
          // Missing yearsInterval
        },
      ];

      const service = new CalculationService(
        expensesWithoutInterval,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );

      const result = service.calculateAll();

      // Should handle gracefully and not include invalid one-time expenses
      expect(result.oneTimeOccurrences).toHaveLength(0);
      expect(result.aggregatedResults.breakdown.oneTime).toBe(0);
    });

    it("should handle very short retirement duration", () => {
      const service = new CalculationService(
        mockExpenses,
        defaultParams.retirementAge,
        1, // Only 1 year retirement
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );

      const result = service.calculateAll();

      expect(result.yearlyBreakdowns).toHaveLength(1);
      expect(result.yearlyBreakdowns[0].year).toBe(1);
      expect(result.yearlyBreakdowns[0].age).toBe(defaultParams.retirementAge);
    });
  });

  describe("Utility Functions", () => {
    it("should calculateCosts function work correctly", () => {
      const result = calculateCosts(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );

      expect(result).toHaveProperty("currentMonthlyCost");
      expect(result).toHaveProperty("totalRetirementCost");
      expect(result).toHaveProperty("breakdown");
      expect(result).toHaveProperty("detailedBreakdown");
      expect(result.currentMonthlyCost).toBe(10000);
      expect(result.totalRetirementCost).toBeGreaterThan(0);
    });

    it("should calculateYearlyBreakdowns function work correctly", () => {
      const result = calculateYearlyBreakdowns(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );

      expect(result).toHaveLength(defaultParams.retirementDuration);
      expect(result[0]).toHaveProperty("year");
      expect(result[0]).toHaveProperty("age");
      expect(result[0]).toHaveProperty("totalYearlyExpenses");
      expect(result[0].year).toBe(1);
      expect(result[0].age).toBe(defaultParams.retirementAge);
    });

    it("should use default INFLATION_RATE when not provided", () => {
      const resultWithDefault = calculateCosts(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge
      );

      const resultWithExplicit = calculateCosts(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        INFLATION_RATE * 100 // Convert to percentage
      );

      expect(resultWithDefault.totalRetirementCost).toBeCloseTo(
        resultWithExplicit.totalRetirementCost,
        2
      );
    });

    it("should use default annual return when not provided", () => {
      const resultWithDefault = calculateCosts(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate
      );

      const resultWithExplicit = calculateCosts(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate,
        12 // Default annual return
      );

      expect(resultWithDefault.totalRetirementCost).toBeCloseTo(
        resultWithExplicit.totalRetirementCost,
        2
      );
    });
  });

  describe("Inflation Calculations", () => {
    it("should calculate inflation multiplier correctly", () => {
      const service = new CalculationService(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        6, // 6% inflation
        defaultParams.annualReturn
      );

      const result = service.calculateAll();

      // After 30 years (retirement age - current age), with 6% inflation
      // Multiplier should be approximately (1.06)^30 ≈ 5.74
      const expectedMultiplier = Math.pow(1.06, 30);

      // Check that adjusted costs are significantly higher than current costs
      expect(result.costResults.adjustedMonthlyCost).toBeGreaterThan(
        result.costResults.currentMonthlyCost * 5
      );
    });

    it("should handle high inflation rates", () => {
      const service = new CalculationService(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        20, // 20% inflation
        defaultParams.annualReturn
      );

      const result = service.calculateAll();

      // With high inflation, adjusted costs should be much higher
      expect(result.costResults.adjustedMonthlyCost).toBeGreaterThan(
        result.costResults.currentMonthlyCost * 100
      );
    });
  });

  describe("Expense Category Breakdown", () => {
    it("should categorize expenses correctly in yearly breakdowns", () => {
      const service = new CalculationService(
        mockExpenses,
        defaultParams.retirementAge,
        defaultParams.retirementDuration,
        defaultParams.currentAge,
        defaultParams.inflationRate,
        defaultParams.annualReturn
      );

      const result = service.calculateAll();
      const firstYear = result.yearlyBreakdowns[0];

      expect(firstYear.expensesByCategory.monthly).toHaveLength(1);
      expect(firstYear.expensesByCategory.yearly).toHaveLength(1);
      expect(firstYear.expensesByCategory.monthly[0].name).toBe("Food");
      expect(firstYear.expensesByCategory.yearly[0].name).toBe("Vacation");

      // Check if one-time expenses appear in the correct years
      const yearWithLaptop = result.yearlyBreakdowns.find(
        (breakdown) => breakdown.expensesByCategory.oneTime.length > 0
      );
      expect(yearWithLaptop).toBeDefined();
      if (yearWithLaptop) {
        expect(yearWithLaptop.expensesByCategory.oneTime[0].name).toBe(
          "Laptop"
        );
      }
    });
  });
});
